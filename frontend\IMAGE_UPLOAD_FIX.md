# Product Image Upload Implementation Fix

## Проблема

Зображення не зберігалися при створенні/редагуванні продуктів:
- Файли не відправлялися на бекенд
- Не зберігалися в базі даних
- Не зберігалися на сервері

## Причини проблем

### 1. Неправильні API методи
- Використовувався неправильний ендпоінт
- Неправильна назва поля для файлу в FormData
- Відсутність реального завантаження файлів

### 2. Відсутність логіки завантаження
- `handleImageUpload` тільки логував файли
- `handleMetaImageUpload` не завантажував файли
- Немає завантаження після створення продукту

## Рішення

### 1. Виправлено API сервіси

**Додано правильні методи в `products.js`:**

```javascript
// Для множинних зображень (використовує /images ендпоінт)
async uploadProductImage(productId, imageFile) {
  const formData = new FormData();
  formData.append('images', imageFile); // Змінено з 'image' на 'images'
  
  const response = await api.post(`/api/admin/products/${productId}/images`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
  return response.data;
}

// Для одного зображення (використовує /images/single ендпоінт)
async uploadProductImageSingle(productId, imageFile) {
  const formData = new FormData();
  formData.append('file', imageFile); // Single file endpoint очікує 'file'
  
  const response = await api.post(`/api/admin/products/${productId}/images/single`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
  return response.data;
}

// Для meta зображення
async uploadProductMetaImage(productId, imageFile) {
  const formData = new FormData();
  formData.append('image', imageFile);
  
  const response = await api.post(`/api/admin/products/${productId}/meta-image`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
  return response.data;
}
```

### 2. Реалізовано завантаження зображень в ProductEdit

**Оновлено `handleImageUpload`:**
```javascript
const handleImageUpload = async (files) => {
  console.log('Uploading images:', files);
  
  // Якщо створюємо новий продукт, зберігаємо файли для пізнішого завантаження
  if (props.isCreate) {
    console.log('Creating product - images will be uploaded after product creation');
    return;
  }
  
  // Якщо редагуємо існуючий продукт, завантажуємо зображення відразу
  if (currentProductId.value) {
    try {
      for (const file of files) {
        if (file.file) {
          const response = await productsService.uploadProductImageSingle(currentProductId.value, file.file);
          if (response && response.success && response.data) {
            file.id = response.data;
            file.uploaded = true;
          }
        }
      }
      // Перезавантажуємо продукт для отримання оновлених зображень
      await loadProduct();
    } catch (error) {
      console.error('Error uploading images:', error);
      error.value = 'Failed to upload images. Please try again.';
    }
  }
};
```

**Оновлено `handleMetaImageUpload`:**
```javascript
const handleMetaImageUpload = async (files) => {
  if (files.length > 0) {
    const file = files[0];
    
    // Якщо створюємо новий продукт, зберігаємо файл для пізнішого завантаження
    if (props.isCreate) {
      console.log('Creating product - meta image will be uploaded after product creation');
      formData.value.metaImage = '';
      return;
    }
    
    // Якщо редагуємо існуючий продукт, завантажуємо meta image відразу
    if (currentProductId.value && file.file) {
      try {
        const response = await productsService.uploadProductMetaImage(currentProductId.value, file.file);
        if (response && response.success && response.data) {
          formData.value.metaImage = response.data;
          file.url = response.data;
          file.uploaded = true;
        }
      } catch (error) {
        console.error('Error uploading meta image:', error);
        error.value = 'Failed to upload meta image. Please try again.';
      }
    }
  }
};
```

### 3. Додано завантаження після створення продукту

**Функція `uploadPendingImages`:**
```javascript
const uploadPendingImages = async (productId) => {
  try {
    console.log('Uploading pending images for product:', productId);
    
    // Завантажуємо звичайні зображення продукту
    if (formData.value.images && formData.value.images.length > 0) {
      for (const image of formData.value.images) {
        if (image.file && !image.uploaded) {
          const response = await productsService.uploadProductImageSingle(productId, image.file);
          if (response && response.success && response.data) {
            image.uploaded = true;
            image.id = response.data;
          }
        }
      }
    }
    
    // Завантажуємо meta зображення
    if (formData.value.metaImageArray && formData.value.metaImageArray.length > 0) {
      const metaImage = formData.value.metaImageArray[0];
      if (metaImage.file && !metaImage.uploaded) {
        const response = await productsService.uploadProductMetaImage(productId, metaImage.file);
        if (response && response.success && response.data) {
          formData.value.metaImage = response.data;
          metaImage.url = response.data;
          metaImage.uploaded = true;
        }
      }
    }
  } catch (error) {
    console.error('Error in uploadPendingImages:', error);
  }
};
```

**Оновлено `handleSubmit`:**
```javascript
if (props.isCreate) {
  result = await productsService.createProduct(productData);
  emit('created', result);

  const productId = result.data?.id || result.id;
  
  // Завантажуємо зображення після створення продукту
  if (productId) {
    await uploadPendingImages(productId);
  }

  // Перенаправляємо на сторінку редагування
  router.push(`/admin/products/${productId}/edit`);
}
```

## Результат

### ✅ Виправлені проблеми:
1. **API ендпоінти**: Використовуються правильні ендпоінти з правильними назвами полів
2. **Завантаження при створенні**: Зображення завантажуються після створення продукту
3. **Завантаження при редагуванні**: Зображення завантажуються відразу при додаванні
4. **Meta зображення**: Окремий ендпоінт для meta зображень
5. **Обробка помилок**: Додана обробка помилок завантаження

### ✅ Підтримувані сценарії:
- Створення продукту з зображеннями
- Редагування існуючого продукту з додаванням зображень
- Завантаження meta зображення
- Автоматичне перезавантаження даних після завантаження

### ✅ API ендпоінти:
- `POST /api/admin/products/{id}/images` - множинні зображення (поле: `images`)
- `POST /api/admin/products/{id}/images/single` - одне зображення (поле: `file`)
- `POST /api/admin/products/{id}/meta-image` - meta зображення (поле: `image`)

### ✅ Структура відповіді:
```javascript
{
  "success": true,
  "data": "image-id-or-url",
  "message": "Зображення успішно завантажено."
}
```

## Тестування

Тепер можна:
1. Створювати продукти з зображеннями - зображення завантажуються після створення
2. Редагувати існуючі продукти - зображення завантажуються відразу
3. Додавати meta зображення
4. Бачити завантажені зображення в інтерфейсі
5. Зображення зберігаються в БД та на сервері

Всі операції з зображеннями працюють коректно з реальним завантаженням на бекенд.
