<template>
  <div class="enhanced-attributes-editor">
    <div class="admin-attributes-header">
      <h4 class="admin-attributes-title">
        <i class="fas fa-tags"></i>
        Product Attributes
      </h4>
    </div>

    <!-- Add New Attribute -->
    <div class="admin-add-attribute">
      <div class="admin-add-form">
        <div class="admin-field-group">
          <label class="admin-field-label">Attribute Name</label>
          <input
            v-model="newKey"
            type="text"
            class="admin-field-input"
            placeholder="e.g., Color, Size, Material"
            @keydown.enter="addAttribute"
          />
        </div>
        <div class="admin-field-group">
          <label class="admin-field-label">Value</label>
          <input
            v-model="newValue"
            type="text"
            class="admin-field-input"
            placeholder="Enter attribute value"
            @keydown.enter="addAttribute"
          />
        </div>
        <button
          type="button"
          class="admin-btn admin-btn-primary"
          @click="addAttribute"
          :disabled="!canAddAttribute"
        >
          <i class="fas fa-plus"></i>
          Add Item
        </button>
      </div>
    </div>

    <!-- Attributes Table -->
    <div v-if="attributeKeys.length > 0" class="admin-attributes-table">
      <table class="admin-table">
        <thead>
          <tr>
            <th class="drag-column"></th>
            <th>Attribute Name</th>
            <th>Values</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody ref="tableBody">
          <tr
            v-for="(key, index) in attributeKeys"
            :key="key"
            class="admin-attribute-row"
            :data-key="key"
            draggable="true"
            @dragstart="handleDragStart($event, index)"
            @dragover="handleDragOver"
            @drop="handleDrop($event, index)"
            @dragend="handleDragEnd"
          >
            <td class="drag-column">
              <i class="fas fa-grip-vertical admin-drag-handle"></i>
            </td>
            <td class="admin-attribute-key">
              <span class="admin-key-name">{{ key }}</span>
            </td>
            <td class="admin-attribute-values">
              <div class="admin-values-list">
                <span
                  v-for="(value, valueIndex) in attributes[key]"
                  :key="`${key}-${valueIndex}`"
                  class="admin-value-tag"
                >
                  {{ value }}
                  <button
                    type="button"
                    class="admin-value-remove"
                    @click="removeValue(key, valueIndex)"
                    :title="`Remove '${value}' from ${key}`"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
            </td>
            <td class="admin-attribute-actions">
              <div class="admin-action-buttons">
                <button
                  type="button"
                  class="admin-btn admin-btn-xs admin-btn-danger"
                  @click="removeAttribute(key)"
                  :title="`Remove entire '${key}' attribute`"
                >
                  <i class="fas fa-trash"></i>
                  Remove
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-else class="admin-empty-state">
      <i class="fas fa-tags admin-empty-icon"></i>
      <p class="admin-empty-text">No attributes added yet</p>
      <p class="admin-empty-subtext">Add attributes to describe your product features</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// State
const newKey = ref('');
const newValue = ref('');
const attributes = ref({});
const draggedIndex = ref(-1);
const tableBody = ref(null);

// Initialize attributes from props
const initializeAttributes = (value) => {
  if (!value) return {};

  // Convert any format to our standard format (key: [values])
  const result = {};

  Object.keys(value).forEach(key => {
    const val = value[key];
    if (Array.isArray(val)) {
      result[key] = [...val];
    } else if (typeof val === 'string') {
      // Split by comma if contains comma, otherwise single value
      result[key] = val.includes(',') ? val.split(',').map(v => v.trim()).filter(v => v) : [val];
    } else {
      result[key] = [String(val)];
    }
  });

  return result;
};

// Initialize
attributes.value = initializeAttributes(props.modelValue);

// Computed
const attributeKeys = computed(() => Object.keys(attributes.value));

const canAddAttribute = computed(() => {
  return newKey.value.trim() && newValue.value.trim();
});

// Methods
const addAttribute = () => {
  const key = newKey.value.trim();
  const value = newValue.value.trim();

  if (!key || !value) return;

  // If key exists, add value to existing array (merge values)
  if (attributes.value[key]) {
    if (!attributes.value[key].includes(value)) {
      attributes.value[key].push(value);
    }
  } else {
    // Create new attribute with single value
    attributes.value[key] = [value];
  }

  // Clear form
  newKey.value = '';
  newValue.value = '';

  // Emit changes
  emitChanges();
};

const removeValue = (key, valueIndex) => {
  if (attributes.value[key] && attributes.value[key].length > valueIndex) {
    attributes.value[key].splice(valueIndex, 1);

    // Remove entire attribute if no values left (auto-cleanup)
    if (attributes.value[key].length === 0) {
      delete attributes.value[key];
    }

    emitChanges();
  }
};

const removeAttribute = (key) => {
  if (confirm(`Are you sure you want to remove the entire '${key}' attribute?`)) {
    delete attributes.value[key];
    emitChanges();
  }
};

// Drag and drop methods
const handleDragStart = (event, index) => {
  draggedIndex.value = index;
  event.dataTransfer.effectAllowed = 'move';
  event.target.style.opacity = '0.5';
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'move';
};

const handleDrop = (event, targetIndex) => {
  event.preventDefault();
  
  if (draggedIndex.value === -1 || draggedIndex.value === targetIndex) {
    return;
  }

  const keys = [...attributeKeys.value];
  const draggedKey = keys[draggedIndex.value];
  
  // Remove dragged item
  keys.splice(draggedIndex.value, 1);
  
  // Insert at new position
  keys.splice(targetIndex, 0, draggedKey);
  
  // Rebuild attributes object in new order
  const newAttributes = {};
  keys.forEach(key => {
    newAttributes[key] = attributes.value[key];
  });
  
  attributes.value = newAttributes;
  emitChanges();
};

const handleDragEnd = (event) => {
  event.target.style.opacity = '';
  draggedIndex.value = -1;
};

const emitChanges = () => {
  emit('update:modelValue', { ...attributes.value });
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  attributes.value = initializeAttributes(newValue);
}, { deep: true, immediate: true });
</script>

<style scoped>
.enhanced-attributes-editor {
  width: 100%;
}

.admin-attributes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--admin-space-md);
}

.admin-attributes-title {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin: 0;
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-add-attribute {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  padding: var(--admin-space-md);
  margin-bottom: var(--admin-space-md);
}

.admin-add-form {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--admin-space-md);
  align-items: end;
}

.admin-field-group {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-field-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
}

.admin-field-input {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-field-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-sm) var(--admin-space-md);
  border: none;
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-medium);
  cursor: pointer;
  transition: all var(--admin-transition-base);
  white-space: nowrap;
}

.admin-btn-primary {
  background: var(--admin-primary);
  color: white;
}

.admin-btn-primary:hover:not(:disabled) {
  background: var(--admin-primary-dark);
}

.admin-btn-danger {
  background: var(--admin-danger);
  color: white;
}

.admin-btn-danger:hover:not(:disabled) {
  background: var(--admin-danger-dark);
}

.admin-btn-xs {
  padding: 4px 8px;
  font-size: var(--admin-text-xs);
}

.admin-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-attributes-table {
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background: var(--admin-bg-secondary);
  padding: var(--admin-space-sm);
  text-align: left;
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  border-bottom: 1px solid var(--admin-border-light);
}

.admin-table td {
  padding: var(--admin-space-sm);
  border-bottom: 1px solid var(--admin-border-light);
  vertical-align: top;
}

.admin-attribute-row:last-child td {
  border-bottom: none;
}

.admin-attribute-row {
  transition: background-color var(--admin-transition-base);
}

.admin-attribute-row:hover {
  background: var(--admin-bg-secondary);
}

.admin-attribute-row[draggable="true"] {
  cursor: move;
}

.drag-column {
  width: 30px;
  text-align: center;
}

.admin-drag-handle {
  color: var(--admin-text-secondary);
  cursor: grab;
  font-size: var(--admin-text-sm);
}

.admin-drag-handle:active {
  cursor: grabbing;
}

.admin-key-name {
  font-weight: var(--admin-font-medium);
  color: var(--admin-text-primary);
}

.admin-values-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-xs);
}

.admin-value-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: 4px 8px;
  background: var(--admin-primary);
  color: white;
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
}

.admin-value-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: var(--admin-text-xs);
  opacity: 0.8;
  transition: opacity var(--admin-transition-base);
}

.admin-value-remove:hover {
  opacity: 1;
}

.admin-action-buttons {
  display: flex;
  gap: var(--admin-space-xs);
}

.admin-empty-state {
  text-align: center;
  padding: var(--admin-space-xl);
  color: var(--admin-text-secondary);
}

.admin-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--admin-space-md);
  opacity: 0.5;
}

.admin-empty-text {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-medium);
  margin-bottom: var(--admin-space-xs);
}

.admin-empty-subtext {
  font-size: var(--admin-text-sm);
}

/* Drag and drop visual feedback */
.admin-attribute-row[style*="opacity: 0.5"] {
  background: var(--admin-bg-disabled);
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-add-form {
    grid-template-columns: 1fr;
    gap: var(--admin-space-sm);
  }

  .admin-table {
    font-size: var(--admin-text-sm);
  }

  .admin-table th,
  .admin-table td {
    padding: var(--admin-space-xs);
  }

  .drag-column {
    width: 25px;
  }

  .admin-values-list {
    flex-direction: column;
    gap: var(--admin-space-xs);
  }

  .admin-action-buttons {
    flex-direction: column;
  }

  .admin-btn-xs {
    padding: 6px 10px;
    font-size: var(--admin-text-xs);
  }
}
</style>
