<template>
  <EnhancedSelector
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    @change="$emit('change', $event)"
    @select="$emit('select', $event)"
    @search="handleSearch"
    :label="label"
    :placeholder="placeholder || 'Search companies...'"
    :required="required"
    :disabled="disabled"
    :error-message="errorMessage"
    :help-text="helpText"
    :items="companies"
    :loading="loading"
    :item-key="'id'"
    :item-name="'name'"
    :item-subtext="getCompanySubtext"
    :filter-function="filterCompanies"
    :max-height="'300px'"
    :show-on-focus="true"
    :min-search-length="0"
  >
    <template #item="{ item }">
      <div class="company-info">
        <span class="company-name">{{ item.name }}</span>
        <span class="company-status" :class="item.isApproved ? 'approved' : 'pending'">
          {{ item.isApproved ? '✓ Approved' : '⏳ Pending' }}
        </span>
      </div>
    </template>

    <template #empty>
      No companies found
    </template>
  </EnhancedSelector>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import EnhancedSelector from './EnhancedSelector.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: 'Company'
  },
  placeholder: {
    type: String,
    default: 'Search companies...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// State
const companies = ref([]);
const loading = ref(false);
const abortController = ref(null);

// Cache for companies data
const companiesCache = {
  data: null,
  timestamp: null,
  ttl: 5 * 60 * 1000 // 5 minutes
};

// Methods
const getCompanySubtext = (company) => {
  return company.isApproved ? '✓ Approved' : '⏳ Pending';
};

const filterCompanies = (companies, query) => {
  if (!query) return companies;
  
  const searchTerm = query.toLowerCase();
  return companies.filter(company => {
    return company.name.toLowerCase().includes(searchTerm) ||
           (company.slug && company.slug.toLowerCase().includes(searchTerm));
  });
};

const loadCompanies = async (force = false) => {
  // Check cache first
  if (!force && companiesCache.data && companiesCache.timestamp) {
    const now = Date.now();
    if (now - companiesCache.timestamp < companiesCache.ttl) {
      companies.value = companiesCache.data;
      return;
    }
  }

  try {
    loading.value = true;
    
    // Cancel previous request
    if (abortController.value) {
      abortController.value.abort();
    }
    
    abortController.value = new AbortController();
    
    const response = await productsService.getCompanies({ 
      pageSize: 1000,
      signal: abortController.value.signal
    });
    
    let companiesData = [];
    if (Array.isArray(response)) {
      companiesData = response;
    } else if (response && Array.isArray(response.data)) {
      companiesData = response.data;
    } else {
      companiesData = [];
    }

    // Sort companies by name for better UX
    companiesData.sort((a, b) => a.name.localeCompare(b.name));
    
    companies.value = companiesData;
    
    // Update cache
    companiesCache.data = companiesData;
    companiesCache.timestamp = Date.now();
    
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('Error loading companies:', error);
      companies.value = [];
    }
  } finally {
    loading.value = false;
  }
};

const handleSearch = (query) => {
  // Emit search event for potential external handling
  emit('search', query);
};

// Watchers
watch(() => props.modelValue, async (newValue) => {
  // Ensure companies are loaded when a value is set
  if (newValue && companies.value.length === 0) {
    await loadCompanies();
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  // Always load companies on mount to ensure we have all options
  await loadCompanies();
});

onBeforeUnmount(() => {
  if (abortController.value) {
    abortController.value.abort();
  }
});

// Expose methods for parent components
defineExpose({
  loadCompanies,
  companies
});
</script>

<style scoped>
.company-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--admin-space-sm);
}

.company-name {
  font-weight: var(--admin-font-medium);
  flex: 1;
}

.company-status {
  font-size: var(--admin-text-sm);
  padding: 2px 6px;
  border-radius: var(--admin-radius-sm);
  flex-shrink: 0;
}

.company-status.approved {
  color: var(--admin-success);
  background: rgba(34, 197, 94, 0.1);
}

.company-status.pending {
  color: var(--admin-warning);
  background: rgba(245, 158, 11, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .company-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-xs);
  }
  
  .company-status {
    font-size: var(--admin-text-xs);
  }
}
</style>
