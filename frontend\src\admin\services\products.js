import api from '@/services/api';
import { getCurrencyEnumValue, transformProductDataForAPI } from '../utils/productConstants.js';

export const productsService = {
  async getProducts(params = {}) {
    try {
      console.log('Fetching products with params:', params);

      // Extract signal from params
      const { signal, ...requestParams } = params;

      // Standardize parameters for the API
      const apiParams = {};

      // Basic pagination parameters
      if (requestParams.page) apiParams.page = requestParams.page;
      if (requestParams.pageSize) apiParams.pageSize = requestParams.pageSize;
      if (requestParams.limit) apiParams.pageSize = requestParams.limit; // Convert limit to pageSize

      // Sorting parameters
      if (requestParams.orderBy) apiParams.orderBy = requestParams.orderBy;
      if (requestParams.sortBy) apiParams.orderBy = requestParams.sortBy; // Convert sortBy to orderBy
      if (requestParams.descending !== undefined) apiParams.descending = requestParams.descending;
      if (requestParams.sortOrder) {
        apiParams.descending = requestParams.sortOrder === 'desc';
      }

      // Search parameter - use 'filter' as expected by the backend
      if (requestParams.search && requestParams.search.trim() !== '') {
        apiParams.filter = requestParams.search.trim();
      } else if (requestParams.filter && requestParams.filter.trim() !== '') {
        apiParams.filter = requestParams.filter.trim();
      }

      // Category filter
      if (params.categoryId && params.categoryId !== '') {
        apiParams.categoryId = params.categoryId;
      }

      // Status filter
      if (params.status && params.status !== '') {
        apiParams.status = params.status;
      }

      // Stock filter
      if (params.stock && params.stock !== '') {
        apiParams.stock = params.stock;
      }

      // First try the admin endpoint (which doesn't exist, so skip to public)
      try {
        console.log('Sending standardized params to admin API:', apiParams);
        // Skip admin endpoint for now and use public endpoint directly
        throw new Error('Admin endpoint not implemented yet');

        // Handle different response structures from backend
        let productsData = [];
        let paginationInfo = {};

        if (response.data) {
          // Check if it's a paginated response
          if (response.data.data && Array.isArray(response.data.data)) {
            productsData = response.data.data;
            paginationInfo = {
              total: response.data.total || response.data.totalItems || 0,
              page: response.data.page || response.data.currentPage || apiParams.page || 1,
              pageSize: response.data.pageSize || response.data.perPage || apiParams.pageSize || 15,
              totalPages: response.data.totalPages || response.data.lastPage || Math.ceil((response.data.total || 0) / (apiParams.pageSize || 15))
            };
          }
          // Check if it's a direct array
          else if (Array.isArray(response.data)) {
            productsData = response.data;
            paginationInfo = {
              total: response.data.length,
              page: apiParams.page || 1,
              pageSize: apiParams.pageSize || 15,
              totalPages: Math.ceil(response.data.length / (apiParams.pageSize || 15))
            };
          }
          // Check if it's wrapped in another structure
          else if (response.data.items && Array.isArray(response.data.items)) {
            productsData = response.data.items;
            paginationInfo = {
              total: response.data.total || response.data.totalItems || response.data.items.length,
              page: response.data.page || response.data.currentPage || apiParams.page || 1,
              pageSize: response.data.pageSize || response.data.perPage || apiParams.pageSize || 15,
              totalPages: response.data.totalPages || response.data.lastPage || Math.ceil((response.data.total || response.data.items.length) / (apiParams.pageSize || 15))
            };
          }
        }

        const result = {
          data: productsData,
          pagination: paginationInfo,
          // For backward compatibility
          items: productsData,
          total: paginationInfo.total,
          currentPage: paginationInfo.page,
          totalPages: paginationInfo.totalPages
        };

        console.log('Products service returning:', {
          itemsCount: productsData.length,
          pagination: paginationInfo,
          totalItems: paginationInfo.total,
          totalPages: paginationInfo.totalPages
        });

        return result;

      } catch (adminError) {
        // If admin endpoint fails, try the public endpoint
        console.warn('Admin products endpoint failed, falling back to public endpoint:', adminError.message);

        try {
          console.log('Using public products endpoint with params:', apiParams);

          // Support for AbortSignal
          const config = { params: apiParams };
          if (signal) {
            config.signal = signal;
          }

          const fallbackResponse = await api.get('/api/products', config);
          console.log('Public products API response:', fallbackResponse.data);

          let productsData = [];
          let paginationInfo = {};

          if (fallbackResponse.data) {
            if (fallbackResponse.data.data && Array.isArray(fallbackResponse.data.data)) {
              productsData = fallbackResponse.data.data;
              paginationInfo = {
                total: fallbackResponse.data.total || fallbackResponse.data.totalItems || 0,
                page: fallbackResponse.data.currentPage || apiParams.page || 1,
                pageSize: fallbackResponse.data.perPage || apiParams.pageSize || 15,
                totalPages: fallbackResponse.data.lastPage || Math.ceil((fallbackResponse.data.total || 0) / (apiParams.pageSize || 15))
              };
            } else if (Array.isArray(fallbackResponse.data)) {
              productsData = fallbackResponse.data;
              paginationInfo = {
                total: fallbackResponse.data.length,
                page: apiParams.page || 1,
                pageSize: apiParams.pageSize || 15,
                totalPages: Math.ceil(fallbackResponse.data.length / (apiParams.pageSize || 15))
              };
            }
          }

          return {
            data: productsData,
            pagination: paginationInfo,
            items: productsData,
            total: paginationInfo.total,
            currentPage: paginationInfo.page,
            totalPages: paginationInfo.totalPages
          };

        } catch (publicError) {
          console.error('Both admin and public products endpoints failed:', publicError.message);

          // Return empty data structure instead of throwing error
          return {
            data: [],
            pagination: {
              total: 0,
              page: apiParams.page || 1,
              pageSize: apiParams.pageSize || 20,
              totalPages: 1
            },
            items: [],
            total: 0,
            currentPage: apiParams.page || 1,
            totalPages: 1
          };
        }
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  async getProductById(id) {
    try {
      const response = await api.get(`/api/admin/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product ${id}:`, error);
      throw error;
    }
  },

  async getProductByIdWithImages(id) {
    try {
      const response = await api.get(`/api/admin/products/${id}/with-images`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product with images ${id}:`, error);
      throw error;
    }
  },

  async createProduct(productData) {
    try {
      console.log('🚀 Creating product with data:', productData);

      // Prepare the command data according to StoreProductCommand structure
      const commandData = {
        companyId: productData.companyId,
        name: productData.name,
        slug: productData.slug,
        description: productData.description || '',
        priceCurrency: getCurrencyEnumValue(productData.priceCurrency),
        priceAmount: parseFloat(productData.priceAmount),
        stock: parseInt(productData.stock) || 0,
        categoryId: productData.categoryId,
        status: parseInt(productData.status) || 0, // Default to pending
        attributes: productData.attributes ? (typeof productData.attributes === 'string' ? JSON.parse(productData.attributes) : productData.attributes) : {},
        metaTitle: productData.metaTitle || '',
        metaDescription: productData.metaDescription || '',
        metaImage: productData.metaImage || ''
      };

      console.log('📋 Prepared command data:', commandData);

      const response = await api.post('/api/admin/products', commandData);
      console.log('✅ Product creation response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating product:', error);
      if (error.response) {
        console.error('📋 Response status:', error.response.status);
        console.error('📋 Response data:', error.response.data);
        console.error('📋 Response headers:', error.response.headers);
      }
      throw error;
    }
  },

  async updateProduct(id, productData) {
    try {
      console.log('🚀 Updating product with data:', productData);

      // Prepare the command data according to UpdateProductCommand structure
      const commandData = {
        name: productData.name || null,
        slug: productData.slug || null,
        description: productData.description || null,
        priceCurrency: productData.priceCurrency ? getCurrencyEnumValue(productData.priceCurrency) : null,
        priceAmount: productData.priceAmount ? parseFloat(productData.priceAmount) : null,
        stock: productData.stock ? parseInt(productData.stock) : null,
        categoryId: productData.categoryId || null,
        attributes: productData.attributes ? (typeof productData.attributes === 'string' ? JSON.parse(productData.attributes) : productData.attributes) : null,
        status: productData.status !== undefined ? parseInt(productData.status) : null,
        isApproved: productData.isApproved || null,
        approvedAt: productData.approvedAt || null,
        approvedByUserId: productData.approvedByUserId || null,
        metaTitle: productData.metaTitle || null,
        metaDescription: productData.metaDescription || null,
        metaImage: productData.metaImage || null
      };

      console.log('📋 Prepared command data:', commandData);

      const response = await api.put(`/api/admin/products/${id}`, commandData);
      console.log('✅ Product update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating product:', error);
      if (error.response) {
        console.error('📋 Response status:', error.response.status);
        console.error('📋 Response data:', error.response.data);
        console.error('📋 Response headers:', error.response.headers);
      }
      throw error;
    }
  },

  async deleteProduct(id) {
    try {
      const response = await api.delete(`/api/admin/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting product ${id}:`, error);
      throw error;
    }
  },

  async uploadProductImage(productId, imageFile) {
    try {
      const formData = new FormData();
      formData.append('images', imageFile); // Changed from 'image' to 'images' to match controller

      const response = await api.post(`/api/admin/products/${productId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error uploading image for product ${productId}:`, error);
      throw error;
    }
  },

  async uploadProductImageSingle(productId, imageFile) {
    try {
      const formData = new FormData();
      formData.append('file', imageFile); // Single file endpoint expects 'file'

      const response = await api.post(`/api/admin/products/${productId}/images/single`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error uploading single image for product ${productId}:`, error);
      throw error;
    }
  },

  async uploadProductMetaImage(productId, imageFile) {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      const response = await api.post(`/api/admin/products/${productId}/meta-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error uploading meta image for product ${productId}:`, error);
      throw error;
    }
  },

  async deleteProductImage(productId, imageId) {
    try {
      const response = await api.delete(`/api/admin/products/${productId}/images/${imageId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting image ${imageId} for product ${productId}:`, error);
      throw error;
    }
  },

  async setMainProductImage(productId, imageId) {
    try {
      const response = await api.patch(`/api/admin/products/${productId}/images/${imageId}/main`);
      return response.data;
    } catch (error) {
      console.error(`Error setting main image ${imageId} for product ${productId}:`, error);
      throw error;
    }
  },

  async toggleProductStatus(id, status) {
    try {
      const response = await api.patch(`/api/admin/products/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.error(`Error updating status for product ${id}:`, error);
      throw error;
    }
  },

  async getProductStats() {
    try {
      const response = await api.get('/api/admin/products/stats');
      return response.data;
    } catch (error) {
      console.error('Error fetching product stats:', error);
      throw error;
    }
  },

  async getCategories(params = {}) {
    try {
      console.log('Fetching categories with params:', params);

      // Standardize parameters for the API
      const apiParams = {
        pageSize: params.pageSize || 1000, // Get all categories by default
        page: params.page || 1
      };

      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      }

      // Support for AbortSignal
      const config = { params: apiParams };
      if (params.signal) {
        config.signal = params.signal;
      }

      const response = await api.get('/api/admin/categories', config);
      console.log('Categories API response:', response.data);

      // Extract categories from response - API returns PaginatedResponse<CategoryResponse>
      let categoriesData = [];
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        categoriesData = response.data.data;
      } else if (Array.isArray(response.data)) {
        categoriesData = response.data;
      }

      // Create simple flat list - just return all categories as separate items
      const flatCategories = categoriesData.map(category => ({
        id: category.id,
        name: category.name,
        parentId: category.parentId,
        slug: category.slug
      }));

      // Sort by name for better UX
      flatCategories.sort((a, b) => a.name.localeCompare(b.name));

      console.log('Processed categories:', flatCategories.length);
      return flatCategories;

    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  // New methods for enhanced functionality
  async getCompanies(params = {}) {
    try {
      console.log('Fetching companies with params:', params);

      const apiParams = {
        pageSize: params.pageSize || 1000,
        page: params.page || 1
      };

      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      }

      // Support for AbortSignal
      const config = { params: apiParams };
      if (params.signal) {
        config.signal = params.signal;
      }

      const response = await api.get('/api/admin/companies', config);
      console.log('Companies API response:', response.data);

      let companiesData = [];
      // API returns ApiResponse<PaginatedResponse<CompanyResponse>>
      if (response.data && response.data.success && response.data.data && response.data.data.data && Array.isArray(response.data.data.data)) {
        companiesData = response.data.data.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        companiesData = response.data.data;
      } else if (Array.isArray(response.data)) {
        companiesData = response.data;
      }

      const companies = companiesData.map(company => ({
        id: company.id,
        name: company.name,
        slug: company.slug,
        isApproved: company.isApproved
      }));

      companies.sort((a, b) => a.name.localeCompare(b.name));
      console.log('Processed companies:', companies.length);
      return companies;

    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  },

  async getCompanyById(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching company ${id}:`, error);
      throw error;
    }
  },

  async getCategoryById(id) {
    try {
      const response = await api.get(`/api/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  },

  async updateProductStatus(id, updateData) {
    try {
      // If updateData is just a number, convert to object
      const data = typeof updateData === 'number'
        ? { status: updateData }
        : updateData;

      const response = await api.patch(`/api/admin/products/${id}/status`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating status for product ${id}:`, error);
      throw error;
    }
  },

  async approveProduct(id) {
    try {
      const response = await api.patch(`/api/admin/products/${id}/approve`);
      return response.data;
    } catch (error) {
      console.error(`Error approving product ${id}:`, error);
      throw error;
    }
  },

  async rejectProduct(id, reason = '') {
    try {
      const response = await api.patch(`/api/admin/products/${id}/reject`, { reason });
      return response.data;
    } catch (error) {
      console.error(`Error rejecting product ${id}:`, error);
      throw error;
    }
  }
};
