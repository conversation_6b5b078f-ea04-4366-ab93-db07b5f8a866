# Product Images Display Fix

## Проблема

Зображення не відображалися правильно на сторінках view та edit:
- Використовувався неправильний API ендпоінт без зображень
- Неправильна конвертація структури даних з API
- Компоненти очікували інший формат зображень

## Причини проблем

### 1. Неправильний API ендпоінт
- Використовувався `getProductById()` замість `getProductByIdWithImages()`
- API повертав продукт без зображень

### 2. Неправильна структура даних
- **API формат**: `ProductImageResponse` з полями `Id`, `Image`, `IsMain`, `Order`, `AltText`
- **Компонент очікує**: об'єкти з полями `id`, `url`, `name`, `isMain`, `order`

### 3. Відсутність конвертації
- Зображення з API не конвертувалися в потрібний формат
- `AdminProductImagesViewer` завжди повертав порожній масив

## Рішення

### 1. Додано правильний API метод

**В `products.js`:**
```javascript
async getProductByIdWithImages(id) {
  try {
    const response = await api.get(`/api/admin/products/${id}/with-images`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching product with images ${id}:`, error);
    throw error;
  }
}
```

### 2. Оновлено всі компоненти для використання правильного API

**ProductEdit.vue:**
```javascript
const response = await productsService.getProductByIdWithImages(currentProductId.value);
```

**ProductView.vue:**
```javascript
const response = await productsService.getProductByIdWithImages(productId);
```

**ProductDetail.vue:**
```javascript
const data = await productsService.getProductByIdWithImages(productId.value);
```

### 3. Додано функцію конвертації зображень

**В ProductEdit.vue:**
```javascript
const convertImagesFromAPI = (apiImages) => {
  if (!Array.isArray(apiImages)) return [];
  
  return apiImages.map(apiImage => ({
    id: apiImage.id || apiImage.Id,
    url: apiImage.image || apiImage.Image || apiImage.imageUrl || apiImage.ImageUrl,
    name: apiImage.altText || apiImage.AltText || `Image ${apiImage.order || apiImage.Order || 1}`,
    uploaded: true,
    isMain: apiImage.isMain || apiImage.IsMain || false,
    order: apiImage.order || apiImage.Order || 0,
    altText: apiImage.altText || apiImage.AltText || ''
  }));
};
```

**Використання:**
```javascript
formData.value = {
  ...product,
  images: convertImagesFromAPI(product.images || []),
  // ... інші поля
};
```

### 4. Виправлено AdminProductImagesViewer

**Замінено:**
```javascript
const additionalImages = computed(() => {
  return []; // Завжди порожній масив
});
```

**На:**
```javascript
const additionalImages = computed(() => {
  if (!props.product?.images || !Array.isArray(props.product.images)) {
    return [];
  }
  
  // Convert API images to display format
  return props.product.images.map(image => ({
    id: image.id || image.Id,
    url: image.image || image.Image || image.imageUrl || image.ImageUrl,
    alt: image.altText || image.AltText || 'Product image',
    isMain: image.isMain || image.IsMain || false,
    order: image.order || image.Order || 0
  })).sort((a, b) => a.order - b.order);
});
```

### 5. Виправлено ProductDetail.vue

**Додано конвертацію зображень:**
```javascript
// Ensure images is an array and convert from API format
if (!Array.isArray(product.value.images)) {
  product.value.images = [];
} else {
  // Convert API images to display format
  product.value.images = product.value.images.map(image => ({
    id: image.id || image.Id,
    url: image.image || image.Image || image.imageUrl || image.ImageUrl,
    alt: image.altText || image.AltText || 'Product image',
    isMain: image.isMain || image.IsMain || false,
    order: image.order || image.Order || 0
  })).sort((a, b) => a.order - b.order);
}
```

## Структура даних

### API Response (ProductWithImagesResponse)
```javascript
{
  "id": "guid",
  "name": "Product Name",
  // ... інші поля продукту
  "metaImage": "https://example.com/meta-image.jpg",
  "images": [
    {
      "id": "guid",
      "productId": "guid", 
      "image": "https://example.com/image1.jpg",
      "imageUrl": "https://example.com/image1.jpg", // alias
      "order": 0,
      "isMain": true,
      "altText": "Product image",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Конвертований формат для компонентів
```javascript
{
  "id": "guid",
  "url": "https://example.com/image1.jpg",
  "name": "Product image",
  "uploaded": true,
  "isMain": true,
  "order": 0,
  "altText": "Product image"
}
```

## Результат

### ✅ Виправлені проблеми:
1. **API ендпоінт**: Використовується `/with-images` для отримання зображень
2. **Конвертація даних**: Правильна конвертація між API та компонентами
3. **Відображення зображень**: Зображення відображаються на всіх сторінках
4. **Сортування**: Зображення сортуються за полем `order`

### ✅ Підтримувані сценарії:
- Перегляд продукту з зображеннями (ProductView)
- Редагування продукту з зображеннями (ProductEdit)
- Детальний перегляд продукту (ProductDetail)
- Відображення галереї зображень (AdminProductImagesViewer)

### ✅ Оновлені компоненти:
- `ProductEdit.vue` - правильне завантаження та конвертація зображень
- `ProductView.vue` - використання API з зображеннями
- `ProductDetail.vue` - конвертація зображень для відображення
- `AdminProductImagesViewer.vue` - правильне відображення галереї
- `products.js` - додано метод `getProductByIdWithImages()`

## Тестування

Тепер можна:
1. Переглядати продукти з зображеннями на сторінці view
2. Редагувати продукти з відображенням існуючих зображень
3. Бачити галерею зображень в AdminProductImagesViewer
4. Правильне сортування зображень за порядком
5. Відображення головного зображення (isMain)

Всі зображення тепер правильно завантажуються та відображаються на всіх сторінках!
