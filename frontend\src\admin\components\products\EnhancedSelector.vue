<template>
  <div class="enhanced-selector">
    <label v-if="label" class="admin-form-label" :class="{ 'admin-form-label--required': required }">
      {{ label }}
    </label>

    <div class="admin-selector-container" :class="{ 'admin-selector-container--error': hasError }">
      <div class="admin-dropdown-wrapper">
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          class="admin-form-input"
          :class="{ 'admin-form-input--error': hasError }"
          :placeholder="placeholder || 'Search...'"
          :disabled="disabled || loading"
          @focus="handleFocus"
          @blur="handleBlur"
          @keydown="handleKeydown"
          @input="handleInput"
          autocomplete="off"
        />

        <div v-if="loading" class="admin-selector-loading">
          <i class="fas fa-spinner fa-spin"></i>
        </div>

        <div v-if="showDropdown && filteredItems.length > 0" class="admin-dropdown">
          <div class="admin-dropdown-list" ref="dropdownList">
            <div
              v-for="(item, index) in filteredItems"
              :key="getItemKey(item)"
              class="admin-dropdown-item"
              :class="{ 'admin-dropdown-item--highlighted': index === highlightedIndex }"
              @mousedown="selectItem(item)"
              @mouseenter="highlightedIndex = index"
            >
              <slot name="item" :item="item" :index="index">
                <div class="item-info">
                  <span class="item-name">{{ getItemName(item) }}</span>
                  <span v-if="getItemSubtext(item)" class="item-subtext">
                    {{ getItemSubtext(item) }}
                  </span>
                </div>
              </slot>
            </div>
          </div>
        </div>

        <div v-else-if="showDropdown && searchQuery && filteredItems.length === 0" class="admin-dropdown">
          <div class="admin-dropdown-empty">
            <slot name="empty">
              No items found
            </slot>
          </div>
        </div>
      </div>
    </div>

    <div v-if="errorMessage" class="admin-form-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ errorMessage }}
    </div>
    
    <div v-if="helpText" class="admin-form-help">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Search...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  items: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  // Function to get item key for v-for
  itemKey: {
    type: [String, Function],
    default: 'id'
  },
  // Function to get item display name
  itemName: {
    type: [String, Function],
    default: 'name'
  },
  // Function to get item subtext (optional)
  itemSubtext: {
    type: [String, Function],
    default: null
  },
  // Function to filter items
  filterFunction: {
    type: Function,
    default: null
  },
  // Maximum height for dropdown
  maxHeight: {
    type: String,
    default: '300px'
  },
  // Whether to show dropdown on focus even without search
  showOnFocus: {
    type: Boolean,
    default: true
  },
  // Minimum characters to start search
  minSearchLength: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select', 'search']);

// State
const searchQuery = ref('');
const showDropdown = ref(false);
const highlightedIndex = ref(-1);
const searchInput = ref(null);
const dropdownList = ref(null);
const abortController = ref(null);

// Computed
const hasError = computed(() => !!props.errorMessage);

const selectedItem = computed(() => {
  if (!props.modelValue || !props.items.length) return null;
  return props.items.find(item => getItemKey(item) === props.modelValue);
});

const filteredItems = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < props.minSearchLength) {
    return props.showOnFocus ? props.items : [];
  }

  if (props.filterFunction) {
    return props.filterFunction(props.items, searchQuery.value);
  }

  // Default filtering
  const query = searchQuery.value.toLowerCase();
  return props.items.filter(item => {
    const name = getItemName(item).toLowerCase();
    const subtext = getItemSubtext(item);
    return name.includes(query) || (subtext && subtext.toLowerCase().includes(query));
  });
});

// Helper methods
const getItemKey = (item) => {
  if (typeof props.itemKey === 'function') {
    return props.itemKey(item);
  }
  return item[props.itemKey];
};

const getItemName = (item) => {
  if (typeof props.itemName === 'function') {
    return props.itemName(item);
  }
  return item[props.itemName];
};

const getItemSubtext = (item) => {
  if (!props.itemSubtext) return null;
  if (typeof props.itemSubtext === 'function') {
    return props.itemSubtext(item);
  }
  return item[props.itemSubtext];
};

// Methods
const selectItem = (item) => {
  const key = getItemKey(item);
  emit('update:modelValue', key);
  emit('change', key);
  emit('select', item);

  searchQuery.value = getItemName(item);
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const handleFocus = () => {
  if (props.showOnFocus) {
    showDropdown.value = true;
  }
};

const handleBlur = () => {
  setTimeout(() => {
    showDropdown.value = false;
    highlightedIndex.value = -1;
  }, 200);
};

const handleInput = () => {
  showDropdown.value = true;
  highlightedIndex.value = -1;
  emit('search', searchQuery.value);
};

const handleKeydown = (event) => {
  if (!showDropdown.value) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredItems.value.length - 1);
      scrollToHighlighted();
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      scrollToHighlighted();
      break;
    case 'Enter':
      event.preventDefault();
      if (highlightedIndex.value >= 0 && filteredItems.value[highlightedIndex.value]) {
        selectItem(filteredItems.value[highlightedIndex.value]);
      }
      break;
    case 'Escape':
      showDropdown.value = false;
      highlightedIndex.value = -1;
      break;
  }
};

const scrollToHighlighted = () => {
  nextTick(() => {
    if (dropdownList.value && highlightedIndex.value >= 0) {
      const items = dropdownList.value.children;
      if (items[highlightedIndex.value]) {
        items[highlightedIndex.value].scrollIntoView({
          block: 'nearest'
        });
      }
    }
  });
};

// Watchers
watch(() => props.modelValue, async (newValue) => {
  if (newValue && props.items.length > 0) {
    const item = props.items.find(item => getItemKey(item) === newValue);
    if (item) {
      searchQuery.value = getItemName(item);
    }
  } else {
    searchQuery.value = '';
  }
}, { immediate: true });

watch(() => props.items, (newItems) => {
  // Update search query when items load and we have a selected value
  if (props.modelValue && newItems.length > 0) {
    const item = newItems.find(item => getItemKey(item) === props.modelValue);
    if (item) {
      searchQuery.value = getItemName(item);
    }
  }
}, { immediate: true });

watch(searchQuery, () => {
  highlightedIndex.value = -1;
});

// Cleanup
onBeforeUnmount(() => {
  if (abortController.value) {
    abortController.value.abort();
  }
});
</script>

<style scoped>
.enhanced-selector {
  width: 100%;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-selector-container {
  position: relative;
}

.admin-dropdown-wrapper {
  position: relative;
}

.admin-form-input {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-input--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-input:disabled {
  background: var(--admin-bg-disabled);
  color: var(--admin-text-disabled);
  cursor: not-allowed;
}

.admin-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 2px;
}

.admin-dropdown-list {
  max-height: v-bind(maxHeight);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--admin-border-light) transparent;
}

.admin-dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.admin-dropdown-list::-webkit-scrollbar-track {
  background: transparent;
}

.admin-dropdown-list::-webkit-scrollbar-thumb {
  background: var(--admin-border-light);
  border-radius: 3px;
}

.admin-dropdown-list::-webkit-scrollbar-thumb:hover {
  background: var(--admin-text-secondary);
}

.admin-dropdown-item {
  padding: var(--admin-space-sm);
  cursor: pointer;
  border-bottom: 1px solid var(--admin-border-light);
  transition: background-color var(--admin-transition-base);
}

.admin-dropdown-item:last-child {
  border-bottom: none;
}

.admin-dropdown-item:hover,
.admin-dropdown-item--highlighted {
  background: var(--admin-bg-secondary);
}

.admin-dropdown-empty {
  padding: var(--admin-space-sm);
  text-align: center;
  color: var(--admin-text-secondary);
  font-style: italic;
}

.item-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--admin-space-sm);
}

.item-name {
  font-weight: var(--admin-font-medium);
  flex: 1;
}

.item-subtext {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-secondary);
  flex-shrink: 0;
}

.admin-selector-loading {
  position: absolute;
  right: var(--admin-space-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-primary);
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  color: var(--admin-danger);
  font-size: var(--admin-text-sm);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
}

/* Responsive design */
@media (max-width: 768px) {
  .admin-dropdown-list {
    max-height: 200px;
  }

  .item-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-xs);
  }

  .item-subtext {
    font-size: var(--admin-text-xs);
  }
}
</style>
