<template>
  <EnhancedSelector
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    @change="$emit('change', $event)"
    @select="$emit('select', $event)"
    @search="handleSearch"
    :label="label"
    :placeholder="placeholder || 'Search categories...'"
    :required="required"
    :disabled="disabled"
    :error-message="errorMessage"
    :help-text="helpText"
    :items="categories"
    :loading="loading"
    :item-key="'id'"
    :item-name="getCategoryDisplayName"
    :item-subtext="getCategorySubtext"
    :filter-function="filterCategories"
    :max-height="'300px'"
    :show-on-focus="true"
    :min-search-length="0"
  >
    <template #item="{ item }">
      <div class="category-info">
        <span class="category-name">{{ getCategoryDisplayName(item) }}</span>
        <span class="category-type" :class="item.parentId ? 'subcategory' : 'main'">
          {{ item.parentId ? 'Subcategory' : 'Main' }}
        </span>
      </div>
    </template>

    <template #empty>
      No categories found
    </template>
  </EnhancedSelector>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import EnhancedSelector from './EnhancedSelector.vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: 'Category'
  },
  placeholder: {
    type: String,
    default: 'Search categories...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// State
const categories = ref([]);
const loading = ref(false);
const abortController = ref(null);

// Cache for categories data
const categoriesCache = {
  data: null,
  timestamp: null,
  ttl: 5 * 60 * 1000 // 5 minutes
};

// Methods
const getCategoryDisplayName = (category) => {
  // Show hierarchy for better understanding
  if (category.parentName) {
    return `${category.parentName} → ${category.name}`;
  }
  return category.name;
};

const getCategorySubtext = (category) => {
  return category.parentId ? 'Subcategory' : 'Main';
};

const filterCategories = (categories, query) => {
  if (!query) return categories;
  
  const searchTerm = query.toLowerCase();
  return categories.filter(category => {
    return category.name.toLowerCase().includes(searchTerm) ||
           (category.slug && category.slug.toLowerCase().includes(searchTerm)) ||
           (category.parentName && category.parentName.toLowerCase().includes(searchTerm));
  });
};

const loadCategories = async (force = false) => {
  // Check cache first
  if (!force && categoriesCache.data && categoriesCache.timestamp) {
    const now = Date.now();
    if (now - categoriesCache.timestamp < categoriesCache.ttl) {
      categories.value = categoriesCache.data;
      return;
    }
  }

  try {
    loading.value = true;
    
    // Cancel previous request
    if (abortController.value) {
      abortController.value.abort();
    }
    
    abortController.value = new AbortController();
    
    const response = await productsService.getCategories({ 
      pageSize: 1000,
      signal: abortController.value.signal
    });
    
    let categoriesData = [];
    if (Array.isArray(response)) {
      categoriesData = response;
    } else if (response && Array.isArray(response.data)) {
      categoriesData = response.data;
    } else {
      categoriesData = [];
    }

    // Sort categories: main categories first, then subcategories, all alphabetically
    categoriesData.sort((a, b) => {
      // First sort by type (main categories first)
      if (!a.parentId && b.parentId) return -1;
      if (a.parentId && !b.parentId) return 1;
      
      // Then sort alphabetically
      if (a.parentId && b.parentId) {
        // For subcategories, sort by parent name first, then by category name
        const parentComparison = (a.parentName || '').localeCompare(b.parentName || '');
        if (parentComparison !== 0) return parentComparison;
      }
      
      return a.name.localeCompare(b.name);
    });
    
    categories.value = categoriesData;
    
    // Update cache
    categoriesCache.data = categoriesData;
    categoriesCache.timestamp = Date.now();
    
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('Error loading categories:', error);
      categories.value = [];
    }
  } finally {
    loading.value = false;
  }
};

const handleSearch = (query) => {
  // Emit search event for potential external handling
  emit('search', query);
};

// Watchers
watch(() => props.modelValue, async (newValue) => {
  // Ensure categories are loaded when a value is set
  if (newValue && categories.value.length === 0) {
    await loadCategories();
  }
}, { immediate: true });

// Lifecycle
onMounted(() => {
  loadCategories();
});

onBeforeUnmount(() => {
  if (abortController.value) {
    abortController.value.abort();
  }
});

// Expose methods for parent components
defineExpose({
  loadCategories,
  categories
});
</script>

<style scoped>
.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--admin-space-sm);
}

.category-name {
  font-weight: var(--admin-font-medium);
  flex: 1;
}

.category-type {
  font-size: var(--admin-text-sm);
  padding: 2px 6px;
  border-radius: var(--admin-radius-sm);
  flex-shrink: 0;
}

.category-type.main {
  color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.1);
}

.category-type.subcategory {
  color: var(--admin-secondary);
  background: rgba(107, 114, 128, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--admin-space-xs);
  }
  
  .category-type {
    font-size: var(--admin-text-xs);
  }
}
</style>
