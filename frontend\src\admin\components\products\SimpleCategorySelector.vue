<template>
  <div class="simple-category-selector">
    <label v-if="label" class="admin-form-label" :class="{ 'admin-form-label--required': required }">
      {{ label }}
    </label>

    <div class="admin-selector-container" :class="{ 'admin-selector-container--error': hasError }">
      <div class="admin-dropdown-wrapper">
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          class="admin-form-input"
          :class="{ 'admin-form-input--error': hasError }"
          :placeholder="placeholder || 'Search categories...'"
          :disabled="disabled || loading"
          @focus="showDropdown = true"
          @blur="handleBlur"
          @keydown="handleKeydown"
          autocomplete="off"
        />

        <div v-if="loading" class="admin-selector-loading">
          <i class="fas fa-spinner fa-spin"></i>
        </div>

        <div v-if="showDropdown && filteredCategories.length > 0" class="admin-dropdown">
          <div class="admin-dropdown-list">
            <div
              v-for="(category, index) in filteredCategories"
              :key="category.id"
              class="admin-dropdown-item"
              :class="{ 'admin-dropdown-item--highlighted': index === highlightedIndex }"
              @mousedown="selectCategory(category)"
              @mouseenter="highlightedIndex = index"
            >
              <div class="category-info">
                <span class="category-name">{{ category.name }}</span>
                <span class="category-type" :class="category.parentId ? 'subcategory' : 'main'">
                  {{ category.parentId ? 'Subcategory' : 'Main' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="showDropdown && searchQuery && filteredCategories.length === 0" class="admin-dropdown">
          <div class="admin-dropdown-empty">
            No categories found
          </div>
        </div>
      </div>
    </div>

    <div v-if="errorMessage" class="admin-form-error">
      <i class="fas fa-exclamation-triangle"></i>
      {{ errorMessage }}
    </div>
    
    <div v-if="helpText" class="admin-form-help">
      {{ helpText }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { productsService } from '../../services/products.js';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Search categories...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'select']);

// State
const categories = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const showDropdown = ref(false);
const highlightedIndex = ref(-1);
const searchInput = ref(null);

// Computed
const hasError = computed(() => !!props.errorMessage);

const selectedCategory = computed(() => {
  if (!props.modelValue) return null;
  return categories.value.find(c => c.id === props.modelValue);
});

const filteredCategories = computed(() => {
  if (!searchQuery.value) return categories.value;

  const query = searchQuery.value.toLowerCase();
  return categories.value.filter(category =>
    category.name.toLowerCase().includes(query)
  );
});

// Methods
const selectCategory = (category) => {
  emit('update:modelValue', category.id);
  emit('change', category.id);
  emit('select', category);

  searchQuery.value = category.name;
  showDropdown.value = false;
  highlightedIndex.value = -1;
};

const handleBlur = () => {
  setTimeout(() => {
    showDropdown.value = false;
    highlightedIndex.value = -1;
  }, 200);
};

const handleKeydown = (event) => {
  if (!showDropdown.value) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredCategories.value.length - 1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (highlightedIndex.value >= 0 && filteredCategories.value[highlightedIndex.value]) {
        selectCategory(filteredCategories.value[highlightedIndex.value]);
      }
      break;
    case 'Escape':
      showDropdown.value = false;
      highlightedIndex.value = -1;
      break;
  }
};

const loadCategories = async () => {
  try {
    loading.value = true;
    const response = await productsService.getCategories({ pageSize: 1000 });
    
    if (Array.isArray(response)) {
      categories.value = response;
    } else if (response && Array.isArray(response.data)) {
      categories.value = response.data;
    } else {
      categories.value = [];
    }
  } catch (error) {
    console.error('Error loading categories:', error);
    categories.value = [];
  } finally {
    loading.value = false;
  }
};

// Watchers
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    // Wait for categories to load if they haven't yet
    if (categories.value.length === 0) {
      await loadCategories();
    }

    const category = categories.value.find(c => c.id === newValue);
    if (category) {
      searchQuery.value = category.name;
    }
  } else {
    searchQuery.value = '';
  }
}, { immediate: true });

watch(categories, (newCategories) => {
  // Update search query when categories load and we have a selected value
  if (props.modelValue && newCategories.length > 0) {
    const category = newCategories.find(c => c.id === props.modelValue);
    if (category) {
      searchQuery.value = category.name;
    }
  }
}, { immediate: true });

watch(searchQuery, () => {
  highlightedIndex.value = -1;
});

// Lifecycle
onMounted(() => {
  loadCategories();
});
</script>

<style scoped>
.simple-category-selector {
  width: 100%;
}

.admin-form-label {
  display: block;
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin-bottom: var(--admin-space-xs);
}

.admin-form-label--required::after {
  content: ' *';
  color: var(--admin-danger);
}

.admin-selector-container {
  position: relative;
}

.admin-dropdown-wrapper {
  position: relative;
}

.admin-form-input {
  width: 100%;
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: all var(--admin-transition-base);
}

.admin-form-input:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 1px var(--admin-primary);
}

.admin-form-input--error {
  border-color: var(--admin-danger);
  box-shadow: 0 0 0 1px var(--admin-danger);
}

.admin-form-input:disabled {
  background: var(--admin-bg-disabled);
  color: var(--admin-text-disabled);
  cursor: not-allowed;
}

.admin-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 2px;
}

.admin-dropdown-list {
  max-height: 200px;
  overflow-y: auto;
}

.admin-dropdown-item {
  padding: var(--admin-space-sm);
  cursor: pointer;
  border-bottom: 1px solid var(--admin-border-light);
  transition: background-color var(--admin-transition-base);
}

.admin-dropdown-item:last-child {
  border-bottom: none;
}

.admin-dropdown-item:hover,
.admin-dropdown-item--highlighted {
  background: var(--admin-bg-secondary);
}

.admin-dropdown-empty {
  padding: var(--admin-space-sm);
  text-align: center;
  color: var(--admin-text-secondary);
  font-style: italic;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: var(--admin-font-medium);
}

.category-type {
  font-size: var(--admin-text-sm);
  padding: 2px 6px;
  border-radius: var(--admin-radius-sm);
}

.category-type.main {
  color: var(--admin-primary);
  background: rgba(59, 130, 246, 0.1);
}

.category-type.subcategory {
  color: var(--admin-secondary);
  background: rgba(107, 114, 128, 0.1);
}

.admin-selector-loading {
  position: absolute;
  right: var(--admin-space-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-primary);
}

.admin-form-error {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-top: var(--admin-space-xs);
  color: var(--admin-danger);
  font-size: var(--admin-text-sm);
}

.admin-form-help {
  margin-top: var(--admin-space-xs);
  color: var(--admin-text-secondary);
  font-size: var(--admin-text-sm);
}
</style>
