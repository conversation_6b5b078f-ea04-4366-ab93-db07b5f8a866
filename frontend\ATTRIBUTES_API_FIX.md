# Product Attributes API Format Fix

## Проблема

При збереженні продукту виникали помилки валідації:

```
Error Response - Status: 400
{
  "errors": {
    "command": ["The command field is required."],
    "$.attributes.Вага": ["The JSON value could not be converted to System.String. Path: $.attributes.Вага"]
  }
}
```

## Причини помилок

### 1. Неправильний формат атрибутів
- **Наш формат**: `{ "Вага": ["1кг", "2кг"] }` (ключ: масив значень)
- **API очікує**: `{ "Вага": "1кг, 2кг" }` (ключ: рядок з комами)

### 2. Проблема з полем command
- API очікує структуру `UpdateProductCommand`
- Потрібна правильна конвертація даних

## Рішення

### 1. Конвертація атрибутів при збереженні

**Додано функцію конвертації в `handleSubmit`:**

```javascript
// Convert attributes from our format (key: [values]) to API format (key: "value1,value2")
const convertedAttributes = {};
if (formData.value.attributes && typeof formData.value.attributes === 'object') {
  Object.keys(formData.value.attributes).forEach(key => {
    const values = formData.value.attributes[key];
    if (Array.isArray(values)) {
      // Join array values with comma
      convertedAttributes[key] = values.join(', ');
    } else {
      // If it's already a string, use as is
      convertedAttributes[key] = String(values);
    }
  });
}

const productData = {
  ...formData.value,
  attributes: convertedAttributes
};
```

### 2. Конвертація атрибутів при завантаженні

**Додано функцію `convertAttributesFromAPI`:**

```javascript
const convertAttributesFromAPI = (attributes) => {
  if (!attributes) return {};
  
  let parsedAttributes = attributes;
  
  // If attributes is a string, parse it
  if (typeof attributes === 'string') {
    try {
      parsedAttributes = JSON.parse(attributes);
    } catch (e) {
      console.error('Error parsing attributes:', e);
      return {};
    }
  }
  
  // Convert from API format (key: "value1,value2") to our format (key: [value1, value2])
  const result = {};
  Object.keys(parsedAttributes).forEach(key => {
    const value = parsedAttributes[key];
    if (typeof value === 'string') {
      // Split by comma and trim whitespace
      result[key] = value.split(',').map(v => v.trim()).filter(v => v);
    } else if (Array.isArray(value)) {
      // Already in array format
      result[key] = value;
    } else {
      // Convert to string and then to array
      result[key] = [String(value)];
    }
  });
  
  return result;
};
```

### 3. Оновлено завантаження продукту

**Замінено:**
```javascript
attributes: typeof product.attributes === 'string'
  ? JSON.parse(product.attributes || '{}')
  : product.attributes || {},
```

**На:**
```javascript
attributes: convertAttributesFromAPI(product.attributes),
```

## Результат

### ✅ Виправлені проблеми:
1. **Формат атрибутів**: Тепер правильно конвертується між форматами
2. **API валідація**: Атрибути передаються у правильному форматі
3. **Двостороння конвертація**: Працює як при збереженні, так і при завантаженні

### ✅ Підтримувані сценарії:
- Створення продукту з атрибутами
- Редагування існуючого продукту
- Додавання нових атрибутів
- Видалення атрибутів
- Множинні значення для одного ключа

### ✅ Формати даних:

**В компоненті (для UI):**
```javascript
{
  "Колір": ["Червоний", "Синій", "Зелений"],
  "Розмір": ["S", "M", "L"],
  "Матеріал": ["Бавовна"]
}
```

**В API (для бекенду):**
```javascript
{
  "Колір": "Червоний, Синій, Зелений",
  "Розмір": "S, M, L", 
  "Матеріал": "Бавовна"
}
```

## Тестування

Тепер можна:
1. Створювати продукти з атрибутами
2. Редагувати існуючі продукти
3. Додавати/видаляти атрибути
4. Зберігати без помилок валідації

Всі операції з атрибутами працюють коректно з правильною конвертацією форматів.
