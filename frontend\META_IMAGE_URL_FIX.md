# Meta Image URL Validation Fix

## Проблема

При збереженні продукту виникала помилка валідації для поля MetaImage:

```
FluentValidation.ValidationException: Validation failed:
-- MetaImage: Must be a valid HTTP/HTTPS URL. Severity: Error
```

## Причина проблеми

API очікує валідний HTTP/HTTPS URL для поля `MetaImage`, але компонент міг відправляти:

1. **Blob URLs**: `blob:http://localhost:3001/...` (створені через `URL.createObjectURL()`)
2. **Порожні рядки**: `""` (які не є валідними URLs)
3. **Невалідні URLs**: Будь-які рядки, що не є HTTP/HTTPS URLs

## Рішення

### 1. Додано функцію валідації URL

```javascript
const isValidHttpUrl = (string) => {
  if (!string || typeof string !== 'string') return false;
  
  try {
    const url = new URL(string);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
};
```

### 2. Валідація при збереженні

**В `handleSubmit`:**
```javascript
// Validate and clean metaImage URL
let cleanMetaImage = formData.value.metaImage || '';
if (cleanMetaImage && !isValidHttpUrl(cleanMetaImage)) {
  console.warn('Invalid metaImage URL, setting to empty:', cleanMetaImage);
  cleanMetaImage = '';
}

const productData = {
  ...formData.value,
  attributes: convertedAttributes,
  metaImage: cleanMetaImage
};
```

### 3. Валідація в формі

**В `validateForm`:**
```javascript
if (formData.value.metaImage && !isValidHttpUrl(formData.value.metaImage)) {
  errors.value.metaImage = 'Meta image must be a valid HTTP/HTTPS URL';
}
```

### 4. Виправлено обробку завантаження зображень

**В `handleMetaImageUpload`:**
```javascript
const handleMetaImageUpload = (files) => {
  console.log('Uploading meta image:', files);
  if (files.length > 0) {
    const file = files[0];
    // Only set metaImage if we have a valid HTTP/HTTPS URL
    if (file.url && isValidHttpUrl(file.url)) {
      formData.value.metaImage = file.url;
    } else {
      // If no valid URL, clear metaImage (will be uploaded separately)
      formData.value.metaImage = '';
      console.log('Meta image file selected but no valid URL available, will be uploaded separately');
    }
  }
};
```

### 5. Виправлено watcher для metaImageArray

```javascript
// Watch for metaImageArray changes to sync with metaImage
watch(() => formData.value.metaImageArray, (newImages) => {
  if (newImages && newImages.length > 0) {
    const imageUrl = newImages[0].url || newImages[0].src || '';
    // Only set metaImage if we have a valid HTTP/HTTPS URL
    if (imageUrl && isValidHttpUrl(imageUrl)) {
      formData.value.metaImage = imageUrl;
    } else {
      formData.value.metaImage = '';
    }
  } else {
    formData.value.metaImage = '';
  }
}, { deep: true });
```

### 6. Додано відображення помилки в UI

```vue
<div v-if="errors.metaImage" class="admin-form-error">
  <i class="fas fa-exclamation-triangle"></i>
  {{ errors.metaImage }}
</div>
```

## Результат

### ✅ Виправлені проблеми:
1. **URL валідація**: Тепер перевіряється, що MetaImage є валідним HTTP/HTTPS URL
2. **Безпечне збереження**: Невалідні URLs автоматично очищаються перед відправкою
3. **UI валідація**: Користувач бачить помилку, якщо введе невалідний URL
4. **Обробка файлів**: Правильно обробляються завантажені файли

### ✅ Підтримувані сценарії:
- Збереження продукту без MetaImage (порожнє поле)
- Збереження з валідним HTTP/HTTPS URL
- Автоматичне очищення невалідних URLs
- Відображення помилок валідації користувачу

### ✅ Валідні формати MetaImage:
- `https://example.com/image.jpg` ✅
- `http://example.com/image.png` ✅
- `""` (порожній рядок) ✅
- `null` або `undefined` ✅

### ❌ Невалідні формати (автоматично очищаються):
- `blob:http://localhost:3001/...` ❌
- `data:image/jpeg;base64,...` ❌
- `file:///path/to/image.jpg` ❌
- `ftp://example.com/image.jpg` ❌
- `invalid-url` ❌

## Тестування

Тепер можна:
1. Створювати продукти без MetaImage
2. Створювати продукти з валідним MetaImage URL
3. Редагувати існуючі продукти
4. Зберігати без помилок валідації MetaImage

Всі операції з MetaImage працюють коректно з правильною валідацією URLs.
